2025-07-18 18:13:53,060 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-07-18 18:20:22,550 [INFO] django.server: "OPTIONS /api/user/credits/ HTTP/1.1" 200 0
2025-07-18 18:20:22,551 [INFO] django.server: "OPTIONS /api/user/credits/ HTTP/1.1" 200 0
2025-07-18 18:20:22,674 [WARNING] django.server: "GET /api/user/credits/ HTTP/1.1" 401 183
2025-07-18 18:20:22,687 [WARNING] django.server: "GET /api/user/credits/ HTTP/1.1" 401 183
2025-07-18 18:20:22,789 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-18 18:20:22,811 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-18 18:20:49,998 [INFO] django.server: "OPTIONS /api/signup/ HTTP/1.1" 200 0
2025-07-18 18:20:56,189 [INFO] django.server: "POST /api/signup/ HTTP/1.1" 201 117
2025-07-18 18:23:57,082 [INFO] django.server: "OPTIONS /api/verify-otp/ HTTP/1.1" 200 0
2025-07-18 18:23:57,113 [INFO] django.server: "POST /api/verify-otp/ HTTP/1.1" 200 597
2025-07-18 18:24:18,427 [INFO] django.server: "OPTIONS /api/login/ HTTP/1.1" 200 0
2025-07-18 18:24:18,435 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: False
2025-07-18 18:24:19,640 [INFO] authentication.views: Authenticated user: <EMAIL>
2025-07-18 18:24:19,765 [INFO] authentication.views: Roles for user 1: []
2025-07-18 18:24:19,767 [INFO] django.server: "POST /api/login/ HTTP/1.1" 200 884
2025-07-18 18:24:21,849 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-18 18:24:21,886 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-18 18:24:21,887 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-18 18:24:21,903 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-18 18:25:00,467 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: False
2025-07-18 18:25:01,636 [INFO] authentication.views: Authenticated user: <EMAIL>
2025-07-18 18:25:01,647 [INFO] authentication.views: Roles for user 1: []
2025-07-18 18:25:01,651 [INFO] django.server: "POST /api/login/ HTTP/1.1" 200 884
2025-07-18 18:25:02,132 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-18 18:25:02,160 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-18 18:25:02,164 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-18 18:25:02,181 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-18 18:25:10,917 [INFO] django.server: "OPTIONS /api/update-user-roles/ HTTP/1.1" 200 0
2025-07-18 18:25:10,926 [INFO] authentication.views: Updating user roles with data: {'user_id': 1, 'roles': ['student'], 'active_role': 'student'}
2025-07-18 18:25:10,926 [INFO] authentication.views: Fetching user with ID: 1
2025-07-18 18:25:10,930 [INFO] authentication.views: Received roles: ['student'], active_role: student
2025-07-18 18:25:10,930 [INFO] authentication.views: Active role set to: student
2025-07-18 18:25:10,944 [INFO] authentication.views: Updated roles for user 1: ['student']
2025-07-18 18:25:10,947 [INFO] django.server: "POST /api/update-user-roles/ HTTP/1.1" 200 344
2025-07-18 18:25:35,079 [INFO] django.server: "OPTIONS /api/update-profile/ HTTP/1.1" 200 0
2025-07-18 18:25:35,100 [INFO] django.server: "POST /api/update-profile/ HTTP/1.1" 200 82
2025-07-18 18:25:41,314 [INFO] django.server: "POST /api/update-profile/ HTTP/1.1" 200 82
2025-07-18 18:25:46,155 [INFO] authentication.views: Updating user roles with data: {'user_id': 1, 'roles': ['student'], 'active_role': 'student'}
2025-07-18 18:25:46,157 [INFO] authentication.views: Fetching user with ID: 1
2025-07-18 18:25:46,160 [INFO] authentication.views: Received roles: ['student'], active_role: student
2025-07-18 18:25:46,163 [INFO] authentication.views: Active role set to: student
2025-07-18 18:25:46,177 [INFO] authentication.views: Updated roles for user 1: ['student']
2025-07-18 18:25:46,178 [INFO] django.server: "POST /api/update-user-roles/ HTTP/1.1" 200 343
2025-07-18 18:25:47,347 [INFO] django.server: "OPTIONS /api/update-active-role/ HTTP/1.1" 200 0
2025-07-18 18:25:47,370 [INFO] authentication.views: Updating active role for user 1 to student
2025-07-18 18:25:47,373 [INFO] django.server: "POST /api/update-active-role/ HTTP/1.1" 200 349
2025-07-18 18:26:48,299 [INFO] django.utils.autoreload: C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\promptRightProd\settings.py changed, reloading.
2025-07-18 18:27:05,796 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-07-18 18:27:45,845 [INFO] django.server: "POST /api/signup/ HTTP/1.1" 201 110
2025-07-18 18:28:35,998 [INFO] django.server: "POST /api/verify-otp/ HTTP/1.1" 200 587
2025-07-18 18:28:50,165 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: False
2025-07-18 18:28:51,644 [INFO] authentication.views: Authenticated user: <EMAIL>
2025-07-18 18:28:51,659 [INFO] authentication.views: Roles for user 2: []
2025-07-18 18:28:51,664 [INFO] django.server: "POST /api/login/ HTTP/1.1" 200 874
2025-07-18 18:28:52,222 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-18 18:28:52,247 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-18 18:28:52,510 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-18 18:28:52,538 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-18 18:29:19,837 [INFO] authentication.views: Updating user roles with data: {'user_id': 2, 'roles': ['qp_uploader'], 'active_role': 'qp_uploader'}
2025-07-18 18:29:19,839 [INFO] authentication.views: Fetching user with ID: 2
2025-07-18 18:29:19,843 [INFO] authentication.views: Received roles: ['qp_uploader'], active_role: qp_uploader
2025-07-18 18:29:19,844 [INFO] authentication.views: Active role set to: qp_uploader
2025-07-18 18:29:19,856 [INFO] authentication.views: Updated roles for user 2: ['qp_uploader']
2025-07-18 18:29:19,859 [INFO] django.server: "POST /api/update-user-roles/ HTTP/1.1" 200 342
2025-07-18 18:29:41,729 [INFO] django.server: "POST /api/update-profile/ HTTP/1.1" 200 82
2025-07-18 18:29:48,740 [INFO] authentication.views: Updating user roles with data: {'user_id': 2, 'roles': ['qp_uploader'], 'active_role': 'qp_uploader'}
2025-07-18 18:29:48,741 [INFO] authentication.views: Fetching user with ID: 2
2025-07-18 18:29:48,744 [INFO] authentication.views: Received roles: ['qp_uploader'], active_role: qp_uploader
2025-07-18 18:29:48,745 [INFO] authentication.views: Active role set to: qp_uploader
2025-07-18 18:29:48,757 [INFO] authentication.views: Updated roles for user 2: ['qp_uploader']
2025-07-18 18:29:48,759 [INFO] django.server: "POST /api/update-user-roles/ HTTP/1.1" 200 341
2025-07-18 18:29:49,913 [INFO] authentication.views: Updating active role for user 2 to qp_uploader
2025-07-18 18:29:49,915 [INFO] django.server: "POST /api/update-active-role/ HTTP/1.1" 200 347
2025-07-18 18:29:50,259 [INFO] django.server: "GET /api/grade/checkpermission/?email=eswaredits13%40gmail.com&role=qp_uploader HTTP/1.1" 200 74
2025-07-18 18:29:50,269 [INFO] django.server: "GET /api/grade/checkpermission/?email=eswaredits13%40gmail.com&role=qp_uploader HTTP/1.1" 200 74
2025-07-18 18:29:50,283 [INFO] django.server: "GET /api/grade/checksubmission/?email=eswaredits13%40gmail.com&role=qp_uploader HTTP/1.1" 200 19
2025-07-18 18:29:50,296 [INFO] django.server: "GET /api/grade/checksubmission/?email=eswaredits13%40gmail.com&role=qp_uploader HTTP/1.1" 200 19
2025-07-18 18:30:30,425 [INFO] django.server: "GET /api/grade/checkpermission/?email=eswaredits13%40gmail.com&role=qp_uploader HTTP/1.1" 200 74
2025-07-18 18:30:30,435 [INFO] django.server: "GET /api/grade/checkpermission/?email=eswaredits13%40gmail.com&role=qp_uploader HTTP/1.1" 200 74
2025-07-18 18:30:30,439 [INFO] django.server: "GET /api/grade/checksubmission/?email=eswaredits13%40gmail.com&role=qp_uploader HTTP/1.1" 200 19
2025-07-18 18:30:30,447 [INFO] django.server: "GET /api/grade/checksubmission/?email=eswaredits13%40gmail.com&role=qp_uploader HTTP/1.1" 200 19
2025-07-18 18:47:51,574 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-07-18 18:48:41,927 [INFO] django.server: "OPTIONS /api/login/ HTTP/1.1" 200 0
2025-07-18 18:48:41,947 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: True
2025-07-18 18:48:42,787 [INFO] authentication.views: Authenticated user: <EMAIL>
2025-07-18 18:48:42,788 [INFO] authentication.views: Promoting user admin to admin
2025-07-18 18:48:42,809 [INFO] authentication.views: Roles for user 2: ['qp_uploader', 'admin']
2025-07-18 18:48:42,818 [INFO] django.server: "POST /api/login/ HTTP/1.1" 200 897
2025-07-18 18:48:43,732 [INFO] django.server: "OPTIONS /api/organization/list/ HTTP/1.1" 200 0
2025-07-18 18:48:43,738 [INFO] django.server: "OPTIONS /api/user-count/ HTTP/1.1" 200 0
2025-07-18 18:48:43,749 [INFO] django.server: "OPTIONS /api/user-role-counts/ HTTP/1.1" 200 0
2025-07-18 18:48:43,752 [INFO] django.server: "OPTIONS /api/feedback/list/ HTTP/1.1" 200 0
2025-07-18 18:48:43,754 [INFO] django.server: "OPTIONS /api/grade/main-requests/ HTTP/1.1" 200 0
2025-07-18 18:48:43,756 [INFO] django.server: "OPTIONS /api/user-count/ HTTP/1.1" 200 0
2025-07-18 18:48:43,766 [INFO] django.server: "OPTIONS /api/organization/list/ HTTP/1.1" 200 0
2025-07-18 18:48:43,768 [INFO] django.server: "OPTIONS /api/user-role-counts/ HTTP/1.1" 200 0
2025-07-18 18:48:43,772 [INFO] django.server: "OPTIONS /api/grade/main-requests/ HTTP/1.1" 200 0
2025-07-18 18:48:43,774 [INFO] django.server: "OPTIONS /api/feedback/list/ HTTP/1.1" 200 0
2025-07-18 18:48:44,058 [INFO] django.server: "GET /api/organization/list/ HTTP/1.1" 200 30
2025-07-18 18:48:44,096 [INFO] django.server: "GET /api/user-count/ HTTP/1.1" 200 11
2025-07-18 18:48:44,101 [INFO] django.server: "GET /api/feedback/list/ HTTP/1.1" 200 21
2025-07-18 18:48:44,101 [INFO] django.server: "GET /api/grade/main-requests/ HTTP/1.1" 200 2
2025-07-18 18:48:44,101 [INFO] django.server: "GET /api/user-role-counts/ HTTP/1.1" 200 71
2025-07-18 18:48:44,101 [INFO] django.server: "GET /api/organization/list/ HTTP/1.1" 200 30
2025-07-18 18:48:44,117 [INFO] django.server: "OPTIONS /api/grade/get-evaluators/ HTTP/1.1" 200 0
2025-07-18 18:48:44,125 [INFO] django.server: "GET /api/user-count/ HTTP/1.1" 200 11
2025-07-18 18:48:44,133 [INFO] django.server: "GET /api/feedback/list/ HTTP/1.1" 200 21
2025-07-18 18:48:44,137 [INFO] django.server: "GET /api/grade/main-requests/ HTTP/1.1" 200 2
2025-07-18 18:48:44,140 [INFO] django.server: "GET /api/user-role-counts/ HTTP/1.1" 200 71
2025-07-18 18:48:44,143 [INFO] django.server: "GET /api/grade/get-evaluators/ HTTP/1.1" 200 2
2025-07-18 18:48:44,153 [INFO] django.server: "OPTIONS /api/grade/unassigned-answers/ HTTP/1.1" 200 0
2025-07-18 18:48:44,158 [INFO] django.server: "GET /api/grade/get-evaluators/ HTTP/1.1" 200 2
2025-07-18 18:48:44,169 [INFO] django.server: "GET /api/grade/unassigned-answers/ HTTP/1.1" 200 2
2025-07-18 18:48:44,181 [INFO] django.server: "GET /api/grade/unassigned-answers/ HTTP/1.1" 200 2
2025-07-18 18:49:31,305 [INFO] django.server: "OPTIONS /api/signup/ HTTP/1.1" 200 0
2025-07-18 18:49:43,907 [ERROR] authentication.views: Registration error: [Errno 11001] getaddrinfo failed
2025-07-18 18:49:43,908 [WARNING] django.server: "POST /api/signup/ HTTP/1.1" 400 74
2025-07-18 18:52:49,517 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-07-18 18:53:23,728 [INFO] django.server: "OPTIONS /api/auth/check-user/ HTTP/1.1" 200 0
2025-07-18 18:53:23,759 [INFO] django.server: "POST /api/auth/check-user/ HTTP/1.1" 200 15
2025-07-18 18:53:23,809 [INFO] django.server: "POST /api/organization/register/ HTTP/1.1" 201 160
2025-07-18 18:53:33,697 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: True
2025-07-18 18:53:34,747 [INFO] authentication.views: Authenticated user: <EMAIL>
2025-07-18 18:53:34,764 [INFO] authentication.views: Roles for user 2: ['qp_uploader', 'admin']
2025-07-18 18:53:34,775 [INFO] django.server: "POST /api/login/ HTTP/1.1" 200 897
2025-07-18 18:53:35,712 [INFO] django.server: "GET /api/user-count/ HTTP/1.1" 200 11
2025-07-18 18:53:35,713 [INFO] django.server: "GET /api/user-role-counts/ HTTP/1.1" 200 71
2025-07-18 18:53:35,718 [INFO] django.server: "GET /api/feedback/list/ HTTP/1.1" 200 21
2025-07-18 18:53:35,721 [INFO] django.server: "GET /api/organization/list/ HTTP/1.1" 200 407
2025-07-18 18:53:35,729 [INFO] django.server: "GET /api/grade/main-requests/ HTTP/1.1" 200 2
2025-07-18 18:53:35,752 [INFO] django.server: "GET /api/user-count/ HTTP/1.1" 200 11
2025-07-18 18:53:35,766 [INFO] django.server: "GET /api/user-role-counts/ HTTP/1.1" 200 71
2025-07-18 18:53:35,768 [INFO] django.server: "GET /api/feedback/list/ HTTP/1.1" 200 21
2025-07-18 18:53:35,769 [INFO] django.server: "GET /api/grade/main-requests/ HTTP/1.1" 200 2
2025-07-18 18:53:35,782 [INFO] django.server: "GET /api/organization/list/ HTTP/1.1" 200 407
2025-07-18 18:53:35,786 [INFO] django.server: "GET /api/grade/get-evaluators/ HTTP/1.1" 200 2
2025-07-18 18:53:35,804 [INFO] django.server: "GET /api/grade/get-evaluators/ HTTP/1.1" 200 2
2025-07-18 18:53:35,809 [INFO] django.server: "GET /api/grade/unassigned-answers/ HTTP/1.1" 200 2
2025-07-18 18:53:35,827 [INFO] django.server: "GET /api/grade/unassigned-answers/ HTTP/1.1" 200 2
2025-07-18 18:53:49,368 [INFO] django.server: "OPTIONS /api/organization/verify/1/ HTTP/1.1" 200 0
2025-07-18 18:53:49,409 [INFO] django.server: "POST /api/organization/verify/1/ HTTP/1.1" 200 482
2025-07-18 18:53:59,639 [INFO] django.server: "OPTIONS /api/update-active-role/ HTTP/1.1" 200 0
2025-07-18 18:53:59,666 [INFO] authentication.views: Updating active role for user 2 to qp_uploader
2025-07-18 18:53:59,669 [INFO] django.server: "POST /api/update-active-role/ HTTP/1.1" 200 354
2025-07-18 18:53:59,698 [INFO] django.server: "GET /api/grade/checkpermission/?email=eswaredits13%40gmail.com&role=qp_uploader HTTP/1.1" 200 74
2025-07-18 18:53:59,714 [INFO] django.server: "GET /api/grade/checkpermission/?email=eswaredits13%40gmail.com&role=qp_uploader HTTP/1.1" 200 74
2025-07-18 18:53:59,715 [INFO] django.server: "GET /api/grade/checksubmission/?email=eswaredits13%40gmail.com&role=qp_uploader HTTP/1.1" 200 19
2025-07-18 18:53:59,733 [INFO] django.server: "GET /api/grade/checksubmission/?email=eswaredits13%40gmail.com&role=qp_uploader HTTP/1.1" 200 19
2025-07-18 18:54:14,192 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: False
2025-07-18 18:54:15,150 [INFO] authentication.views: Authenticated user: <EMAIL>
2025-07-18 18:54:15,164 [INFO] authentication.views: Roles for user 3: ['organization']
2025-07-18 18:54:15,168 [INFO] django.server: "POST /api/login/ HTTP/1.1" 200 888
2025-07-18 18:54:15,940 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-18 18:54:15,952 [INFO] django.server: "OPTIONS /api/user/credits/ HTTP/1.1" 200 0
2025-07-18 18:54:15,971 [INFO] django.server: "OPTIONS /api/user/credits/ HTTP/1.1" 200 0
2025-07-18 18:54:15,972 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-18 18:54:15,980 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-18 18:54:15,997 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-18 18:54:18,400 [INFO] django.server: "OPTIONS /api/organization/students/pending_invitations/ HTTP/1.1" 200 0
2025-07-18 18:54:18,401 [INFO] django.server: "OPTIONS /api/organization/students/ HTTP/1.1" 200 0
2025-07-18 18:54:18,431 [INFO] django.server: "OPTIONS /api/organization/hierarchy-levels/ HTTP/1.1" 200 0
2025-07-18 18:54:18,436 [INFO] django.server: "OPTIONS /api/organization/students/ HTTP/1.1" 200 0
2025-07-18 18:54:18,457 [INFO] django.server: "GET /api/organization/students/pending_invitations/ HTTP/1.1" 200 96
2025-07-18 18:54:18,459 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 85
2025-07-18 18:54:18,463 [INFO] django.server: "OPTIONS /api/organization/hierarchy-levels/ HTTP/1.1" 200 0
2025-07-18 18:54:18,465 [INFO] django.server: "OPTIONS /api/organization/students/pending_invitations/ HTTP/1.1" 200 0
2025-07-18 18:54:18,472 [INFO] django.server: "GET /api/organization/hierarchy-levels/ HTTP/1.1" 200 2
2025-07-18 18:54:18,503 [INFO] django.server: "GET /api/organization/students/pending_invitations/ HTTP/1.1" 200 96
2025-07-18 18:54:18,506 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 85
2025-07-18 18:54:18,510 [INFO] django.server: "GET /api/organization/hierarchy-levels/ HTTP/1.1" 200 2
2025-07-18 18:54:31,965 [INFO] django.server: "POST /api/organization/students/ HTTP/1.1" 200 164
2025-07-18 18:54:31,999 [INFO] django.server: "GET /api/organization/students/pending_invitations/ HTTP/1.1" 200 249
2025-07-18 18:55:14,481 [INFO] django.server: "POST /api/organization/students/accept_invitation/205fdfe1-7779-490c-8a7d-48e6c5953130/ HTTP/1.1" 200 145
2025-07-18 18:55:14,515 [INFO] django.server: "POST /api/organization/students/accept_invitation/205fdfe1-7779-490c-8a7d-48e6c5953130/ HTTP/1.1" 200 145
2025-07-18 18:55:37,703 [INFO] django.server: "OPTIONS /api/login/ HTTP/1.1" 200 0
2025-07-18 18:55:37,712 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: False
2025-07-18 18:55:38,639 [INFO] authentication.views: Authenticated user: <EMAIL>
2025-07-18 18:55:38,653 [INFO] authentication.views: Roles for user 3: ['organization']
2025-07-18 18:55:38,656 [INFO] django.server: "POST /api/login/ HTTP/1.1" 200 888
2025-07-18 18:55:39,851 [INFO] django.server: "OPTIONS /api/user/credits/ HTTP/1.1" 200 0
2025-07-18 18:55:39,852 [INFO] django.server: "OPTIONS /api/user/credits/ HTTP/1.1" 200 0
2025-07-18 18:55:39,868 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-18 18:55:39,886 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-18 18:55:40,152 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-18 18:55:40,162 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-18 18:55:42,510 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-18 18:55:42,524 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-18 18:55:44,152 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-18 18:55:44,167 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-18 18:55:44,168 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-18 18:55:44,182 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-18 18:55:45,379 [INFO] django.server: "OPTIONS /api/organization/students/ HTTP/1.1" 200 0
2025-07-18 18:55:45,381 [INFO] django.server: "OPTIONS /api/organization/students/pending_invitations/ HTTP/1.1" 200 0
2025-07-18 18:55:45,406 [INFO] django.server: "OPTIONS /api/organization/students/ HTTP/1.1" 200 0
2025-07-18 18:55:45,407 [INFO] django.server: "OPTIONS /api/organization/hierarchy-levels/ HTTP/1.1" 200 0
2025-07-18 18:55:45,428 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 195
2025-07-18 18:55:45,432 [INFO] django.server: "OPTIONS /api/organization/students/pending_invitations/ HTTP/1.1" 200 0
2025-07-18 18:55:45,434 [INFO] django.server: "GET /api/organization/students/pending_invitations/ HTTP/1.1" 200 96
2025-07-18 18:55:45,441 [INFO] django.server: "GET /api/organization/hierarchy-levels/ HTTP/1.1" 200 2
2025-07-18 18:55:45,446 [INFO] django.server: "OPTIONS /api/organization/hierarchy-levels/ HTTP/1.1" 200 0
2025-07-18 18:55:45,448 [INFO] django.server: "OPTIONS /api/organization/student-hierarchies/student_hierarchies/?student_id=1 HTTP/1.1" 200 0
2025-07-18 18:55:45,468 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 195
2025-07-18 18:55:45,472 [INFO] django.server: "GET /api/organization/students/pending_invitations/ HTTP/1.1" 200 96
2025-07-18 18:55:45,475 [INFO] django.server: "GET /api/organization/hierarchy-levels/ HTTP/1.1" 200 2
2025-07-18 18:55:45,485 [INFO] django.server: "GET /api/organization/student-hierarchies/student_hierarchies/?student_id=1 HTTP/1.1" 200 96
2025-07-18 18:55:45,498 [INFO] django.server: "GET /api/organization/student-hierarchies/student_hierarchies/?student_id=1 HTTP/1.1" 200 96
2025-07-18 18:56:04,879 [INFO] django.server: "OPTIONS /api/organization/tests/ HTTP/1.1" 200 0
2025-07-18 18:56:04,884 [INFO] django.server: "OPTIONS /api/organization/tests/ HTTP/1.1" 200 0
2025-07-18 18:56:04,897 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 195
2025-07-18 18:56:04,897 [INFO] django.server: "GET /api/organization/tests/ HTTP/1.1" 200 2
2025-07-18 18:56:04,919 [INFO] django.server: "GET /api/organization/tests/ HTTP/1.1" 200 2
2025-07-18 18:56:04,923 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 195
2025-07-18 18:56:07,547 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 195
2025-07-18 18:56:07,560 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 195
2025-07-18 18:57:56,877 [INFO] django.server: "GET /api/organization/student-hierarchies/student_hierarchies/?student_id=1 HTTP/1.1" 200 96
2025-07-18 18:57:56,880 [INFO] django.server: "GET /api/organization/hierarchy-levels/ HTTP/1.1" 200 2
2025-07-18 18:58:01,817 [INFO] django.server: "POST /api/organization/tests/ HTTP/1.1" 201 245
2025-07-18 18:58:01,830 [INFO] django.server: "OPTIONS /api/organization/tests/1/upload_question_paper/ HTTP/1.1" 200 0
2025-07-18 18:58:01,914 [INFO] django.server: "POST /api/organization/tests/1/upload_question_paper/ HTTP/1.1" 201 177
2025-07-18 18:58:01,926 [INFO] django.server: "OPTIONS /api/organization/tests/1/assign_students/ HTTP/1.1" 200 0
2025-07-18 18:58:01,987 [INFO] django.server: "POST /api/organization/tests/1/assign_students/ HTTP/1.1" 200 668
2025-07-18 18:58:02,047 [INFO] django.server: "GET /api/organization/tests/ HTTP/1.1" 200 343
2025-07-18 18:58:02,051 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 195
2025-07-18 18:58:02,090 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 195
2025-07-18 18:58:02,095 [INFO] django.server: "GET /api/organization/tests/ HTTP/1.1" 200 343
2025-07-18 18:58:21,074 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: False
2025-07-18 18:58:22,538 [INFO] authentication.views: Authenticated user: <EMAIL>
2025-07-18 18:58:22,555 [INFO] authentication.views: Roles for user 1: ['student', 'organization']
2025-07-18 18:58:22,558 [INFO] django.server: "POST /api/login/ HTTP/1.1" 200 915
2025-07-18 18:58:23,442 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-18 18:58:23,447 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-18 18:58:23,467 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-18 18:58:23,484 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-18 18:58:25,742 [INFO] django.server: "GET /api/grade/sample/?user_id=1 HTTP/1.1" 200 62
2025-07-18 18:58:25,764 [INFO] django.server: "GET /api/grade/sample/?user_id=1 HTTP/1.1" 200 62
2025-07-18 18:58:25,802 [INFO] django.server: "GET /api/grade/sample/?user_id=1 HTTP/1.1" 200 62
2025-07-18 18:58:25,828 [INFO] django.server: "GET /api/grade/sample/?user_id=1 HTTP/1.1" 200 62
2025-07-18 18:58:30,515 [INFO] django.server: "OPTIONS /api/organization/tests/assigned_tests/ HTTP/1.1" 200 0
2025-07-18 18:58:30,515 [INFO] django.server: "OPTIONS /api/organization/tests/assigned_tests/ HTTP/1.1" 200 0
2025-07-18 18:58:30,543 [INFO] django.server: "GET /api/organization/tests/assigned_tests/ HTTP/1.1" 200 371
2025-07-18 18:58:30,557 [INFO] django.server: "OPTIONS /api/organization/tests/1/check_submission/ HTTP/1.1" 200 0
2025-07-18 18:58:30,579 [INFO] django.server: "GET /api/organization/tests/assigned_tests/ HTTP/1.1" 200 371
2025-07-18 18:58:30,601 [INFO] django.server: "GET /api/organization/tests/1/check_submission/ HTTP/1.1" 200 38
2025-07-18 18:58:30,639 [INFO] django.server: "GET /api/organization/tests/1/check_submission/ HTTP/1.1" 200 38
2025-07-18 19:00:49,329 [INFO] django.server: "OPTIONS /api/organization/tests/1/ HTTP/1.1" 200 0
2025-07-18 19:00:49,330 [INFO] django.server: "OPTIONS /api/organization/tests/1/ HTTP/1.1" 200 0
2025-07-18 19:00:49,359 [INFO] django.server: "GET /api/organization/tests/1/ HTTP/1.1" 200 341
2025-07-18 19:00:49,378 [INFO] django.server: "OPTIONS /api/organization/tests/1/questions/ HTTP/1.1" 200 0
2025-07-18 19:00:49,410 [INFO] django.server: "GET /api/organization/tests/1/ HTTP/1.1" 200 341
2025-07-18 19:00:49,431 [INFO] django.server: "GET /api/organization/tests/1/questions/ HTTP/1.1" 200 238
2025-07-18 19:00:49,472 [INFO] django.server: "GET /api/organization/tests/1/questions/ HTTP/1.1" 200 238
2025-07-18 19:01:05,381 [INFO] django.server: "OPTIONS /api/organization/tests/1/submit/ HTTP/1.1" 200 0
2025-07-18 19:01:05,454 [INFO] django.server: "POST /api/organization/tests/1/submit/ HTTP/1.1" 200 75
2025-07-18 19:01:05,490 [INFO] django.server: "OPTIONS /api/grade/sample/?user_id=1 HTTP/1.1" 200 0
2025-07-18 19:01:05,490 [INFO] django.server: "OPTIONS /api/grade/sample/?user_id=1 HTTP/1.1" 200 0
2025-07-18 19:01:05,510 [INFO] django.server: "OPTIONS /api/grade/sample/?user_id=1 HTTP/1.1" 200 0
2025-07-18 19:01:05,514 [INFO] django.server: "OPTIONS /api/grade/sample/?user_id=1 HTTP/1.1" 200 0
2025-07-18 19:01:05,527 [INFO] django.server: "GET /api/grade/sample/?user_id=1 HTTP/1.1" 200 62
2025-07-18 19:01:05,546 [INFO] django.server: "GET /api/grade/sample/?user_id=1 HTTP/1.1" 200 62
2025-07-18 19:01:05,564 [INFO] django.server: "GET /api/grade/sample/?user_id=1 HTTP/1.1" 200 62
2025-07-18 19:01:05,583 [INFO] django.server: "GET /api/grade/sample/?user_id=1 HTTP/1.1" 200 62
2025-07-18 19:01:11,566 [INFO] django.server: "GET /api/organization/tests/assigned_tests/ HTTP/1.1" 200 371
2025-07-18 19:01:11,608 [INFO] django.server: "GET /api/organization/tests/assigned_tests/ HTTP/1.1" 200 371
2025-07-18 19:01:11,609 [INFO] django.server: "GET /api/organization/tests/1/check_submission/ HTTP/1.1" 200 37
2025-07-18 19:01:11,627 [INFO] django.server: "OPTIONS /api/organization/tests/1/results/ HTTP/1.1" 200 0
2025-07-18 19:01:11,648 [INFO] django.server: "GET /api/organization/tests/1/check_submission/ HTTP/1.1" 200 37
2025-07-18 19:01:11,665 [INFO] django.server: "GET /api/organization/tests/1/results/ HTTP/1.1" 200 527
2025-07-18 19:01:11,707 [INFO] django.server: "GET /api/organization/tests/1/results/ HTTP/1.1" 200 527
2025-07-18 19:01:21,529 [INFO] django.server: "GET /api/grade/sample/?user_id=1 HTTP/1.1" 200 62
2025-07-18 19:01:21,554 [INFO] django.server: "GET /api/grade/sample/?user_id=1 HTTP/1.1" 200 62
2025-07-18 19:01:21,579 [INFO] django.server: "GET /api/grade/sample/?user_id=1 HTTP/1.1" 200 62
2025-07-18 19:01:21,604 [INFO] django.server: "GET /api/grade/sample/?user_id=1 HTTP/1.1" 200 62
2025-07-18 19:01:37,668 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: False
2025-07-18 19:01:38,856 [INFO] authentication.views: Authenticated user: <EMAIL>
2025-07-18 19:01:38,873 [INFO] authentication.views: Roles for user 3: ['organization']
2025-07-18 19:01:38,875 [INFO] django.server: "POST /api/login/ HTTP/1.1" 200 888
2025-07-18 19:01:40,068 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-18 19:01:40,077 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-18 19:01:40,091 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-18 19:01:40,095 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-18 19:01:49,218 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 195
2025-07-18 19:01:49,220 [INFO] django.server: "GET /api/organization/students/pending_invitations/ HTTP/1.1" 200 96
2025-07-18 19:01:49,243 [INFO] django.server: "GET /api/organization/hierarchy-levels/ HTTP/1.1" 200 2
2025-07-18 19:01:49,263 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 195
2025-07-18 19:01:49,272 [INFO] django.server: "GET /api/organization/students/pending_invitations/ HTTP/1.1" 200 96
2025-07-18 19:01:49,293 [INFO] django.server: "GET /api/organization/hierarchy-levels/ HTTP/1.1" 200 2
2025-07-18 19:01:49,293 [INFO] django.server: "GET /api/organization/student-hierarchies/student_hierarchies/?student_id=1 HTTP/1.1" 200 96
2025-07-18 19:01:49,312 [INFO] django.server: "GET /api/organization/student-hierarchies/student_hierarchies/?student_id=1 HTTP/1.1" 200 96
2025-07-18 19:02:14,196 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 195
2025-07-18 19:02:14,200 [INFO] django.server: "GET /api/organization/tests/ HTTP/1.1" 200 343
2025-07-18 19:02:14,222 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 195
2025-07-18 19:02:14,231 [INFO] django.server: "GET /api/organization/tests/ HTTP/1.1" 200 343
2025-07-18 19:02:18,628 [INFO] django.server: "OPTIONS /api/grade/get-evaluators/ HTTP/1.1" 200 0
2025-07-18 19:02:18,629 [INFO] django.server: "OPTIONS /api/grade/get-evaluators/ HTTP/1.1" 200 0
2025-07-18 19:02:18,644 [INFO] django.server: "GET /api/grade/get-evaluators/ HTTP/1.1" 200 2
2025-07-18 19:02:18,661 [INFO] django.server: "OPTIONS /api/grade/unassigned-answers/ HTTP/1.1" 200 0
2025-07-18 19:02:18,665 [INFO] django.server: "GET /api/grade/get-evaluators/ HTTP/1.1" 200 2
2025-07-18 19:02:18,677 [INFO] django.server: "GET /api/grade/unassigned-answers/ HTTP/1.1" 200 2
2025-07-18 19:02:18,693 [INFO] django.server: "GET /api/grade/unassigned-answers/ HTTP/1.1" 200 2
2025-07-18 19:02:23,357 [INFO] django.server: "GET /api/organization/tests/ HTTP/1.1" 200 343
2025-07-18 19:02:23,376 [INFO] django.server: "GET /api/organization/tests/ HTTP/1.1" 200 343
2025-07-18 19:02:26,366 [INFO] django.server: "GET /api/organization/tests/1/results/ HTTP/1.1" 200 527
2025-07-18 19:02:37,194 [INFO] django.server: "GET /api/organization/tests/ HTTP/1.1" 200 343
2025-07-18 19:02:37,212 [INFO] django.server: "GET /api/organization/tests/ HTTP/1.1" 200 343
2025-07-18 19:04:41,366 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 195
2025-07-18 19:04:41,368 [INFO] django.server: "GET /api/organization/students/pending_invitations/ HTTP/1.1" 200 96
2025-07-18 19:04:41,392 [INFO] django.server: "GET /api/organization/hierarchy-levels/ HTTP/1.1" 200 2
2025-07-18 19:04:41,396 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 195
2025-07-18 19:04:41,421 [INFO] django.server: "GET /api/organization/students/pending_invitations/ HTTP/1.1" 200 96
2025-07-18 19:04:41,437 [INFO] django.server: "GET /api/organization/student-hierarchies/student_hierarchies/?student_id=1 HTTP/1.1" 200 96
2025-07-18 19:04:41,437 [INFO] django.server: "GET /api/organization/hierarchy-levels/ HTTP/1.1" 200 2
2025-07-18 19:04:41,455 [INFO] django.server: "GET /api/organization/student-hierarchies/student_hierarchies/?student_id=1 HTTP/1.1" 200 96
2025-07-18 19:04:48,379 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 195
2025-07-18 19:04:48,383 [INFO] django.server: "GET /api/organization/tests/ HTTP/1.1" 200 343
2025-07-18 19:04:48,413 [INFO] django.server: "GET /api/organization/tests/ HTTP/1.1" 200 343
2025-07-18 19:04:48,413 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 195
2025-07-18 19:04:49,275 [INFO] django.server: "GET /api/organization/students/pending_invitations/ HTTP/1.1" 200 96
2025-07-18 19:04:49,275 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 195
2025-07-18 19:04:49,277 [INFO] django.server: "GET /api/organization/hierarchy-levels/ HTTP/1.1" 200 2
2025-07-18 19:04:49,311 [INFO] django.server: "GET /api/organization/students/pending_invitations/ HTTP/1.1" 200 96
2025-07-18 19:04:49,316 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 195
2025-07-18 19:04:49,322 [INFO] django.server: "GET /api/organization/hierarchy-levels/ HTTP/1.1" 200 2
2025-07-18 19:04:49,327 [INFO] django.server: "GET /api/organization/student-hierarchies/student_hierarchies/?student_id=1 HTTP/1.1" 200 96
2025-07-18 19:04:49,344 [INFO] django.server: "GET /api/organization/student-hierarchies/student_hierarchies/?student_id=1 HTTP/1.1" 200 96
2025-07-18 19:04:50,811 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-18 19:04:50,818 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-18 19:04:50,828 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-18 19:04:50,837 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-18 19:05:13,573 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-18 19:05:13,576 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-18 19:05:13,594 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-18 19:05:13,599 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-18 19:06:25,661 [INFO] django.server: "OPTIONS /api/organization/hierarchy-levels/tree/ HTTP/1.1" 200 0
2025-07-18 19:06:25,664 [INFO] django.server: "OPTIONS /api/organization/hierarchy-levels/tree/ HTTP/1.1" 200 0
2025-07-18 19:06:25,989 [INFO] django.server: "GET /api/organization/hierarchy-levels/tree/ HTTP/1.1" 200 30
2025-07-18 19:06:26,009 [INFO] django.server: "GET /api/organization/hierarchy-levels/tree/ HTTP/1.1" 200 30
2025-07-18 19:06:26,959 [INFO] django.server: "OPTIONS /api/organization/progress_summary/ HTTP/1.1" 200 0
2025-07-18 19:06:26,961 [INFO] django.server: "OPTIONS /api/organization/progress_summary/ HTTP/1.1" 200 0
2025-07-18 19:06:27,001 [INFO] django.server: "GET /api/organization/progress_summary/ HTTP/1.1" 200 391
2025-07-18 19:06:27,041 [INFO] django.server: "GET /api/organization/progress_summary/ HTTP/1.1" 200 391
2025-07-18 19:06:31,850 [INFO] django.server: "GET /api/organization/students/pending_invitations/ HTTP/1.1" 200 96
2025-07-18 19:06:31,853 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 195
2025-07-18 19:06:31,871 [INFO] django.server: "OPTIONS /api/organization/student-hierarchies/student_hierarchies/?student_id=1 HTTP/1.1" 200 0
2025-07-18 19:06:31,871 [INFO] django.server: "GET /api/organization/hierarchy-levels/ HTTP/1.1" 200 2
2025-07-18 19:06:31,878 [INFO] django.server: "GET /api/organization/students/pending_invitations/ HTTP/1.1" 200 96
2025-07-18 19:06:31,903 [INFO] django.server: "GET /api/organization/student-hierarchies/student_hierarchies/?student_id=1 HTTP/1.1" 200 96
2025-07-18 19:06:31,903 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 195
2025-07-18 19:06:31,929 [INFO] django.server: "GET /api/organization/hierarchy-levels/ HTTP/1.1" 200 2
2025-07-18 19:06:31,929 [INFO] django.server: "GET /api/organization/student-hierarchies/student_hierarchies/?student_id=1 HTTP/1.1" 200 96
2025-07-18 19:06:34,096 [INFO] django.server: "GET /api/organization/progress_summary/ HTTP/1.1" 200 391
2025-07-18 19:06:34,136 [INFO] django.server: "GET /api/organization/progress_summary/ HTTP/1.1" 200 391
2025-07-18 19:06:34,844 [INFO] django.server: "GET /api/organization/hierarchy-levels/tree/ HTTP/1.1" 200 30
2025-07-18 19:06:34,865 [INFO] django.server: "GET /api/organization/hierarchy-levels/tree/ HTTP/1.1" 200 30
2025-07-18 19:06:37,371 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-18 19:06:37,377 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-18 19:06:37,393 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-18 19:06:37,398 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-18 19:06:39,279 [INFO] django.server: "GET /api/organization/students/pending_invitations/ HTTP/1.1" 200 96
2025-07-18 19:06:39,286 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 195
2025-07-18 19:06:39,291 [INFO] django.server: "GET /api/organization/hierarchy-levels/ HTTP/1.1" 200 2
2025-07-18 19:06:39,319 [INFO] django.server: "GET /api/organization/students/pending_invitations/ HTTP/1.1" 200 96
2025-07-18 19:06:39,323 [INFO] django.server: "GET /api/organization/student-hierarchies/student_hierarchies/?student_id=1 HTTP/1.1" 200 96
2025-07-18 19:06:39,333 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 195
2025-07-18 19:06:39,333 [INFO] django.server: "GET /api/organization/hierarchy-levels/ HTTP/1.1" 200 2
2025-07-18 19:06:39,358 [INFO] django.server: "GET /api/organization/student-hierarchies/student_hierarchies/?student_id=1 HTTP/1.1" 200 96
2025-07-18 19:06:44,511 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-18 19:06:44,517 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-18 19:06:44,533 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-18 19:06:44,538 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 17:50:34,331 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-07-19 17:52:39,912 [INFO] django.server: "OPTIONS /api/user/credits/ HTTP/1.1" 200 0
2025-07-19 17:52:39,915 [INFO] django.server: "OPTIONS /api/user/credits/ HTTP/1.1" 200 0
2025-07-19 17:52:40,070 [WARNING] django.server: "GET /api/user/credits/ HTTP/1.1" 401 183
2025-07-19 17:52:40,087 [WARNING] django.server: "GET /api/user/credits/ HTTP/1.1" 401 183
2025-07-19 17:52:40,177 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 17:52:40,192 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 17:52:52,226 [INFO] django.server: "OPTIONS /api/token/refresh/ HTTP/1.1" 200 0
2025-07-19 17:52:52,228 [INFO] django.server: "OPTIONS /api/token/refresh/ HTTP/1.1" 200 0
2025-07-19 17:52:52,263 [INFO] django.server: "POST /api/token/refresh/ HTTP/1.1" 200 483
2025-07-19 17:52:52,263 [INFO] django.server: "POST /api/token/refresh/ HTTP/1.1" 200 483
2025-07-19 17:52:52,287 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 17:52:52,300 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 17:53:03,869 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 17:53:03,881 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 17:53:03,908 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 17:53:03,916 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 17:53:10,095 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 17:53:10,112 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 18:06:24,401 [INFO] django.server: "OPTIONS /api/billing/ HTTP/1.1" 200 0
2025-07-19 18:06:24,402 [INFO] django.server: "OPTIONS /api/billing/ HTTP/1.1" 200 0
2025-07-19 18:06:24,422 [INFO] django.server: "GET /api/billing/ HTTP/1.1" 200 218
2025-07-19 18:06:24,433 [INFO] django.server: "GET /api/billing/ HTTP/1.1" 200 218
2025-07-19 18:09:08,765 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 18:09:11,762 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 18:09:14,761 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 18:10:13,927 [ERROR] django.request: Internal Server Error: /
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: index.html
2025-07-19 18:10:13,931 [ERROR] django.server: "GET / HTTP/1.1" 500 97924
2025-07-19 18:10:14,204 [WARNING] django.server: "GET /favicon.ico HTTP/1.1" 404 20260
2025-07-19 18:10:18,456 [INFO] django.server: "GET /admin HTTP/1.1" 301 0
2025-07-19 18:10:18,530 [INFO] django.server: "GET /admin/ HTTP/1.1" 302 0
2025-07-19 18:10:18,776 [INFO] django.server: "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4169
2025-07-19 18:10:18,796 [INFO] django.server: "GET /static/admin/css/base.css HTTP/1.1" 200 22092
2025-07-19 18:10:18,802 [INFO] django.server: "GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2804
2025-07-19 18:10:18,808 [INFO] django.server: "GET /static/admin/css/login.css HTTP/1.1" 200 951
2025-07-19 18:10:18,809 [INFO] django.server: "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2810
2025-07-19 18:10:18,810 [INFO] django.server: "GET /static/admin/css/responsive.css HTTP/1.1" 200 17972
2025-07-19 18:10:18,810 [INFO] django.server: "GET /static/admin/js/theme.js HTTP/1.1" 200 1653
2025-07-19 18:10:18,817 [INFO] django.server: "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
2025-07-19 18:10:21,955 [INFO] django.server: "POST /admin/login/?next=/admin/ HTTP/1.1" 200 4349
2025-07-19 18:10:28,531 [INFO] django.server: "POST /admin/login/?next=/admin/ HTTP/1.1" 302 0
2025-07-19 18:10:28,629 [INFO] django.server: "GET /admin/ HTTP/1.1" 200 45912
2025-07-19 18:10:28,645 [INFO] django.server: "GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
2025-07-19 18:10:28,675 [INFO] django.server: "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
2025-07-19 18:10:28,675 [INFO] django.server: "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
2025-07-19 18:10:35,999 [INFO] django.server: "GET /admin/authentication/user/ HTTP/1.1" 200 51098
2025-07-19 18:10:36,025 [INFO] django.server: "GET /static/admin/js/jquery.init.js HTTP/1.1" 200 347
2025-07-19 18:10:36,034 [INFO] django.server: "GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 200 9097
2025-07-19 18:10:36,034 [INFO] django.server: "GET /static/admin/js/core.js HTTP/1.1" 200 6208
2025-07-19 18:10:36,044 [INFO] django.server: "GET /admin/jsi18n/ HTTP/1.1" 200 3342
2025-07-19 18:10:36,045 [INFO] django.server: "GET /static/admin/js/actions.js HTTP/1.1" 200 8076
2025-07-19 18:10:36,046 [INFO] django.server: "GET /static/admin/css/changelists.css HTTP/1.1" 200 6878
2025-07-19 18:10:36,051 [INFO] django.server: "GET /static/admin/js/urlify.js HTTP/1.1" 200 7887
2025-07-19 18:10:36,057 [INFO] django.server: "GET /static/admin/js/prepopulate.js HTTP/1.1" 200 1531
2025-07-19 18:10:36,066 [INFO] django.server: "GET /static/admin/img/search.svg HTTP/1.1" 200 458
2025-07-19 18:10:36,074 [INFO] django.server: "GET /static/admin/img/icon-yes.svg HTTP/1.1" 200 436
2025-07-19 18:10:36,088 [INFO] django.server: "GET /static/admin/img/icon-no.svg HTTP/1.1" 200 560
2025-07-19 18:10:36,114 [INFO] django.server: "GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 200 285314
2025-07-19 18:10:36,126 [INFO] django.server: "GET /static/admin/js/filters.js HTTP/1.1" 200 978
2025-07-19 18:10:36,168 [INFO] django.server: "GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 200 325171
2025-07-19 18:10:36,213 [INFO] django.server: "GET /static/admin/img/tooltag-add.svg HTTP/1.1" 200 331
2025-07-19 18:10:36,213 [INFO] django.server: "GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 200 581
2025-07-19 18:10:36,214 [INFO] django.server: "GET /static/admin/img/sorting-icons.svg HTTP/1.1" 200 1097
2025-07-19 18:10:44,403 [INFO] django.server: "GET /admin/ HTTP/1.1" 200 45912
2025-07-19 18:10:55,169 [INFO] django.server: "OPTIONS /api/organization/students/ HTTP/1.1" 200 0
2025-07-19 18:10:55,177 [INFO] django.server: "OPTIONS /api/organization/students/pending_invitations/ HTTP/1.1" 200 0
2025-07-19 18:10:55,188 [INFO] django.server: "OPTIONS /api/organization/hierarchy-levels/ HTTP/1.1" 200 0
2025-07-19 18:10:55,191 [INFO] django.server: "OPTIONS /api/organization/students/ HTTP/1.1" 200 0
2025-07-19 18:10:55,194 [INFO] django.server: "OPTIONS /api/organization/students/pending_invitations/ HTTP/1.1" 200 0
2025-07-19 18:10:55,196 [INFO] django.server: "OPTIONS /api/organization/hierarchy-levels/ HTTP/1.1" 200 0
2025-07-19 18:10:55,549 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 195
2025-07-19 18:10:55,575 [INFO] django.server: "GET /api/organization/hierarchy-levels/ HTTP/1.1" 200 2
2025-07-19 18:10:55,578 [INFO] django.server: "GET /api/organization/students/pending_invitations/ HTTP/1.1" 200 96
2025-07-19 18:10:55,592 [INFO] django.server: "OPTIONS /api/organization/student-hierarchies/student_hierarchies/?student_id=1 HTTP/1.1" 200 0
2025-07-19 18:10:55,618 [INFO] django.server: "GET /api/organization/hierarchy-levels/ HTTP/1.1" 200 2
2025-07-19 18:10:55,660 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 195
2025-07-19 18:10:55,662 [INFO] django.server: "GET /api/organization/students/pending_invitations/ HTTP/1.1" 200 96
2025-07-19 18:10:55,677 [INFO] django.server: "GET /api/organization/student-hierarchies/student_hierarchies/?student_id=1 HTTP/1.1" 200 96
2025-07-19 18:10:55,708 [INFO] django.server: "GET /api/organization/student-hierarchies/student_hierarchies/?student_id=1 HTTP/1.1" 200 96
2025-07-19 18:11:15,233 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 18:11:15,251 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 18:11:15,251 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 18:11:15,268 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 18:11:21,270 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 18:11:21,286 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 18:11:22,678 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 18:11:22,683 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 18:11:22,694 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 18:11:22,703 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 18:11:23,855 [INFO] django.server: "GET /api/organization/hierarchy-levels/ HTTP/1.1" 200 2
2025-07-19 18:11:23,855 [INFO] django.server: "GET /api/organization/students/pending_invitations/ HTTP/1.1" 200 96
2025-07-19 18:11:23,855 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 195
2025-07-19 18:11:23,900 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 195
2025-07-19 18:11:23,903 [INFO] django.server: "GET /api/organization/hierarchy-levels/ HTTP/1.1" 200 2
2025-07-19 18:11:23,905 [INFO] django.server: "GET /api/organization/student-hierarchies/student_hierarchies/?student_id=1 HTTP/1.1" 200 96
2025-07-19 18:11:23,907 [INFO] django.server: "GET /api/organization/students/pending_invitations/ HTTP/1.1" 200 96
2025-07-19 18:11:23,922 [INFO] django.server: "GET /api/organization/student-hierarchies/student_hierarchies/?student_id=1 HTTP/1.1" 200 96
2025-07-19 18:11:29,968 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 18:11:29,968 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 18:11:29,980 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 18:11:29,990 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 18:11:32,710 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 18:11:32,725 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 18:34:28,602 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-07-19 18:35:40,850 [INFO] django.server: "OPTIONS /api/user/credits/ HTTP/1.1" 200 0
2025-07-19 18:35:40,850 [INFO] django.server: "OPTIONS /api/user/credits/ HTTP/1.1" 200 0
2025-07-19 18:35:40,875 [WARNING] django.server: "GET /api/user/credits/ HTTP/1.1" 401 183
2025-07-19 18:35:40,883 [WARNING] django.server: "GET /api/user/credits/ HTTP/1.1" 401 183
2025-07-19 18:35:41,121 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 18:35:41,136 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 18:35:48,478 [INFO] django.server: "OPTIONS /api/login/ HTTP/1.1" 200 0
2025-07-19 18:35:48,502 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: False
2025-07-19 18:35:49,854 [INFO] authentication.views: Authenticated user: <EMAIL>
2025-07-19 18:35:49,893 [INFO] authentication.views: Roles for user 3: ['organization']
2025-07-19 18:35:49,896 [INFO] django.server: "POST /api/login/ HTTP/1.1" 200 888
2025-07-19 18:35:50,315 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 18:35:50,362 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 18:35:50,364 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 18:35:50,383 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 18:35:55,284 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 18:35:55,299 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 19:24:17,556 [INFO] django.utils.autoreload: C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\authentication\models.py changed, reloading.
2025-07-19 19:24:43,595 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-07-19 19:30:50,250 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-07-19 19:32:18,011 [WARNING] django.server: "GET /api/user/credits/ HTTP/1.1" 401 183
2025-07-19 19:32:18,019 [WARNING] django.server: "GET /api/user/credits/ HTTP/1.1" 401 183
2025-07-19 19:32:18,183 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 19:32:18,206 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 19:32:24,548 [INFO] django.server: "OPTIONS /api/token/refresh/ HTTP/1.1" 200 0
2025-07-19 19:32:24,554 [INFO] django.server: "OPTIONS /api/token/refresh/ HTTP/1.1" 200 0
2025-07-19 19:32:24,608 [INFO] django.server: "POST /api/token/refresh/ HTTP/1.1" 200 483
2025-07-19 19:32:24,608 [INFO] django.server: "POST /api/token/refresh/ HTTP/1.1" 200 483
2025-07-19 19:32:24,793 [ERROR] django.request: Internal Server Error: /api/user/credits/
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 354, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: authentication_user.profile_image

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 503, in dispatch
    self.initial(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 420, in initial
    self.perform_authentication(request)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 330, in perform_authentication
    request.user
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\request.py", line 232, in user
    self._authenticate()
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\request.py", line 385, in _authenticate
    user_auth_tuple = authenticator.authenticate(self)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework_simplejwt\authentication.py", line 51, in authenticate
    return self.get_user(validated_token), validated_token
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework_simplejwt\authentication.py", line 130, in get_user
    user = self.user_model.objects.get(**{api_settings.USER_ID_FIELD: user_id})
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 645, in get
    num = len(clone)
          ^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 382, in __len__
    self._fetch_all()
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 1928, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
              ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\sql\compiler.py", line 1574, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 354, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: authentication_user.profile_image
2025-07-19 19:32:24,805 [ERROR] django.server: "GET /api/user/credits/ HTTP/1.1" 500 225726
2025-07-19 19:32:24,915 [ERROR] django.request: Internal Server Error: /api/user/credits/
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 354, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: authentication_user.profile_image

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 503, in dispatch
    self.initial(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 420, in initial
    self.perform_authentication(request)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 330, in perform_authentication
    request.user
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\request.py", line 232, in user
    self._authenticate()
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\request.py", line 385, in _authenticate
    user_auth_tuple = authenticator.authenticate(self)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework_simplejwt\authentication.py", line 51, in authenticate
    return self.get_user(validated_token), validated_token
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework_simplejwt\authentication.py", line 130, in get_user
    user = self.user_model.objects.get(**{api_settings.USER_ID_FIELD: user_id})
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 645, in get
    num = len(clone)
          ^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 382, in __len__
    self._fetch_all()
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 1928, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
              ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\sql\compiler.py", line 1574, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 354, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: authentication_user.profile_image
2025-07-19 19:32:24,919 [ERROR] django.server: "GET /api/user/credits/ HTTP/1.1" 500 225726
2025-07-19 19:32:26,810 [ERROR] django.request: Internal Server Error: /api/user/credits/
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 354, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: authentication_user.profile_image

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 503, in dispatch
    self.initial(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 420, in initial
    self.perform_authentication(request)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 330, in perform_authentication
    request.user
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\request.py", line 232, in user
    self._authenticate()
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\request.py", line 385, in _authenticate
    user_auth_tuple = authenticator.authenticate(self)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework_simplejwt\authentication.py", line 51, in authenticate
    return self.get_user(validated_token), validated_token
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework_simplejwt\authentication.py", line 130, in get_user
    user = self.user_model.objects.get(**{api_settings.USER_ID_FIELD: user_id})
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 645, in get
    num = len(clone)
          ^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 382, in __len__
    self._fetch_all()
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 1928, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
              ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\sql\compiler.py", line 1574, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 354, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: authentication_user.profile_image
2025-07-19 19:32:26,813 [ERROR] django.server: "GET /api/user/credits/ HTTP/1.1" 500 225726
2025-07-19 19:32:26,958 [ERROR] django.request: Internal Server Error: /api/user/credits/
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 354, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: authentication_user.profile_image

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 503, in dispatch
    self.initial(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 420, in initial
    self.perform_authentication(request)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 330, in perform_authentication
    request.user
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\request.py", line 232, in user
    self._authenticate()
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\request.py", line 385, in _authenticate
    user_auth_tuple = authenticator.authenticate(self)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework_simplejwt\authentication.py", line 51, in authenticate
    return self.get_user(validated_token), validated_token
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework_simplejwt\authentication.py", line 130, in get_user
    user = self.user_model.objects.get(**{api_settings.USER_ID_FIELD: user_id})
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 645, in get
    num = len(clone)
          ^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 382, in __len__
    self._fetch_all()
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 1928, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
              ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\sql\compiler.py", line 1574, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 354, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: authentication_user.profile_image
2025-07-19 19:32:26,967 [ERROR] django.server: "GET /api/user/credits/ HTTP/1.1" 500 225726
2025-07-19 19:32:32,379 [ERROR] django.request: Internal Server Error: /api/login/
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 354, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: authentication_user.profile_image

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 503, in dispatch
    self.initial(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 420, in initial
    self.perform_authentication(request)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 330, in perform_authentication
    request.user
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\request.py", line 232, in user
    self._authenticate()
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\request.py", line 385, in _authenticate
    user_auth_tuple = authenticator.authenticate(self)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\authentication.py", line 127, in authenticate
    if not user or not user.is_active:
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\utils\functional.py", line 251, in inner
    self._setup()
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\utils\functional.py", line 404, in _setup
    self._wrapped = self._setupfunc()
                    ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\contrib\auth\middleware.py", line 37, in <lambda>
    request.user = SimpleLazyObject(lambda: get_user(request))
                                            ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\contrib\auth\middleware.py", line 17, in get_user
    request._cached_user = auth.get_user(request)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\contrib\auth\__init__.py", line 216, in get_user
    user = backend.get_user(user_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\contrib\auth\backends.py", line 157, in get_user
    user = UserModel._default_manager.get(pk=user_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 645, in get
    num = len(clone)
          ^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 382, in __len__
    self._fetch_all()
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 1928, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
              ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\sql\compiler.py", line 1574, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 354, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: authentication_user.profile_image
2025-07-19 19:32:32,385 [ERROR] django.server: "POST /api/login/ HTTP/1.1" 500 244459
2025-07-19 19:32:44,532 [ERROR] django.request: Internal Server Error: /api/login/
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 354, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: authentication_user.profile_image

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 503, in dispatch
    self.initial(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 420, in initial
    self.perform_authentication(request)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 330, in perform_authentication
    request.user
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\request.py", line 232, in user
    self._authenticate()
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\request.py", line 385, in _authenticate
    user_auth_tuple = authenticator.authenticate(self)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\authentication.py", line 127, in authenticate
    if not user or not user.is_active:
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\utils\functional.py", line 251, in inner
    self._setup()
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\utils\functional.py", line 404, in _setup
    self._wrapped = self._setupfunc()
                    ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\contrib\auth\middleware.py", line 37, in <lambda>
    request.user = SimpleLazyObject(lambda: get_user(request))
                                            ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\contrib\auth\middleware.py", line 17, in get_user
    request._cached_user = auth.get_user(request)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\contrib\auth\__init__.py", line 216, in get_user
    user = backend.get_user(user_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\contrib\auth\backends.py", line 157, in get_user
    user = UserModel._default_manager.get(pk=user_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 645, in get
    num = len(clone)
          ^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 382, in __len__
    self._fetch_all()
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 1928, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
              ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\sql\compiler.py", line 1574, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 354, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: authentication_user.profile_image
2025-07-19 19:32:44,537 [ERROR] django.server: "POST /api/login/ HTTP/1.1" 500 244459
2025-07-19 19:33:26,478 [INFO] django.utils.autoreload: C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\authentication\models.py changed, reloading.
2025-07-19 19:33:28,758 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-07-19 19:34:00,163 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: False
2025-07-19 19:34:01,042 [INFO] authentication.views: Authenticated user: <EMAIL>
2025-07-19 19:34:01,057 [INFO] authentication.views: Roles for user 3: ['organization']
2025-07-19 19:34:01,064 [INFO] django.server: "POST /api/login/ HTTP/1.1" 200 888
2025-07-19 19:34:01,346 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 19:34:01,359 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 19:34:01,642 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 19:34:01,654 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 19:35:02,637 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 19:35:02,648 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 19:35:02,657 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 19:35:02,670 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 19:35:10,930 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: False
2025-07-19 19:35:11,842 [INFO] authentication.views: Authenticated user: <EMAIL>
2025-07-19 19:35:11,858 [INFO] authentication.views: Roles for user 3: ['organization']
2025-07-19 19:35:11,861 [INFO] django.server: "POST /api/login/ HTTP/1.1" 200 888
2025-07-19 19:35:12,158 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 19:35:12,195 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 19:35:12,197 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 19:35:12,210 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 19:38:06,005 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 19:38:06,011 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 19:38:06,049 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 19:38:06,060 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 19:38:11,546 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: False
2025-07-19 19:38:12,489 [INFO] authentication.views: Authenticated user: <EMAIL>
2025-07-19 19:38:12,506 [INFO] authentication.views: Roles for user 3: ['organization']
2025-07-19 19:38:12,509 [INFO] django.server: "POST /api/login/ HTTP/1.1" 200 888
2025-07-19 19:38:12,761 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 19:38:12,771 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 19:38:12,773 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 19:38:12,785 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 19:43:23,039 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 19:43:23,045 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 19:43:23,326 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 19:43:23,334 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 19:49:53,893 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 19:49:53,906 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 19:49:54,172 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 19:49:54,188 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 19:50:00,737 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 19:50:00,750 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 20:34:50,324 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-07-19 20:35:51,899 [INFO] django.server: "OPTIONS /api/user/credits/ HTTP/1.1" 200 0
2025-07-19 20:35:51,901 [INFO] django.server: "OPTIONS /api/user/credits/ HTTP/1.1" 200 0
2025-07-19 20:35:51,972 [WARNING] django.server: "GET /api/user/credits/ HTTP/1.1" 401 183
2025-07-19 20:35:51,982 [WARNING] django.server: "GET /api/user/credits/ HTTP/1.1" 401 183
2025-07-19 20:35:52,168 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 20:35:52,172 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 20:35:56,810 [INFO] django.server: "POST /api/token/refresh/ HTTP/1.1" 200 483
2025-07-19 20:35:56,811 [INFO] django.server: "POST /api/token/refresh/ HTTP/1.1" 200 483
2025-07-19 20:35:56,826 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 20:35:56,845 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 20:44:54,114 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-07-19 20:48:16,126 [INFO] django.server: "OPTIONS /api/user/credits/ HTTP/1.1" 200 0
2025-07-19 20:48:16,129 [INFO] django.server: "OPTIONS /api/user/credits/ HTTP/1.1" 200 0
2025-07-19 20:48:16,153 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 20:48:16,162 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 20:48:16,388 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 20:48:16,397 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 20:48:20,388 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 20:48:20,405 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 20:54:31,752 [INFO] django.utils.autoreload: C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\authentication\models.py changed, reloading.
2025-07-19 20:54:35,748 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-07-19 20:55:34,990 [ERROR] django.request: Internal Server Error: /api/user/credits/
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 354, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: authentication_user.profile_picture

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 503, in dispatch
    self.initial(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 420, in initial
    self.perform_authentication(request)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 330, in perform_authentication
    request.user
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\request.py", line 232, in user
    self._authenticate()
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\request.py", line 385, in _authenticate
    user_auth_tuple = authenticator.authenticate(self)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework_simplejwt\authentication.py", line 51, in authenticate
    return self.get_user(validated_token), validated_token
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework_simplejwt\authentication.py", line 130, in get_user
    user = self.user_model.objects.get(**{api_settings.USER_ID_FIELD: user_id})
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 645, in get
    num = len(clone)
          ^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 382, in __len__
    self._fetch_all()
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 1928, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
              ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\sql\compiler.py", line 1574, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 354, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: authentication_user.profile_picture
2025-07-19 20:55:35,030 [ERROR] django.server: "GET /api/user/credits/ HTTP/1.1" 500 224873
2025-07-19 20:55:46,918 [ERROR] django.request: Internal Server Error: /api/user/credits/
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 354, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: authentication_user.profile_picture

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 503, in dispatch
    self.initial(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 420, in initial
    self.perform_authentication(request)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 330, in perform_authentication
    request.user
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\request.py", line 232, in user
    self._authenticate()
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\request.py", line 385, in _authenticate
    user_auth_tuple = authenticator.authenticate(self)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework_simplejwt\authentication.py", line 51, in authenticate
    return self.get_user(validated_token), validated_token
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework_simplejwt\authentication.py", line 130, in get_user
    user = self.user_model.objects.get(**{api_settings.USER_ID_FIELD: user_id})
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 645, in get
    num = len(clone)
          ^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 382, in __len__
    self._fetch_all()
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 1928, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
              ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\sql\compiler.py", line 1574, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 354, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: authentication_user.profile_picture
2025-07-19 20:55:46,926 [ERROR] django.server: "GET /api/user/credits/ HTTP/1.1" 500 224873
2025-07-19 20:55:47,151 [ERROR] django.request: Internal Server Error: /api/user/credits/
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 354, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: authentication_user.profile_picture

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 503, in dispatch
    self.initial(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 420, in initial
    self.perform_authentication(request)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 330, in perform_authentication
    request.user
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\request.py", line 232, in user
    self._authenticate()
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\request.py", line 385, in _authenticate
    user_auth_tuple = authenticator.authenticate(self)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework_simplejwt\authentication.py", line 51, in authenticate
    return self.get_user(validated_token), validated_token
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework_simplejwt\authentication.py", line 130, in get_user
    user = self.user_model.objects.get(**{api_settings.USER_ID_FIELD: user_id})
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 645, in get
    num = len(clone)
          ^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 382, in __len__
    self._fetch_all()
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 1928, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
              ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\sql\compiler.py", line 1574, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 354, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: authentication_user.profile_picture
2025-07-19 20:55:47,164 [ERROR] django.server: "GET /api/user/credits/ HTTP/1.1" 500 224873
2025-07-19 20:59:25,090 [INFO] django.server: "OPTIONS /api/login/ HTTP/1.1" 200 0
2025-07-19 20:59:25,097 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: False
2025-07-19 20:59:25,114 [ERROR] authentication.views: Login error: no such column: authentication_user.profile_picture
2025-07-19 20:59:25,115 [WARNING] django.server: "POST /api/login/ HTTP/1.1" 400 86
2025-07-19 20:59:34,376 [INFO] django.server: "OPTIONS /api/login/ HTTP/1.1" 200 0
2025-07-19 20:59:34,482 [ERROR] django.request: Internal Server Error: /api/login/
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 354, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: authentication_user.profile_picture

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 503, in dispatch
    self.initial(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 420, in initial
    self.perform_authentication(request)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 330, in perform_authentication
    request.user
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\request.py", line 232, in user
    self._authenticate()
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\request.py", line 385, in _authenticate
    user_auth_tuple = authenticator.authenticate(self)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\authentication.py", line 127, in authenticate
    if not user or not user.is_active:
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\utils\functional.py", line 251, in inner
    self._setup()
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\utils\functional.py", line 404, in _setup
    self._wrapped = self._setupfunc()
                    ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\contrib\auth\middleware.py", line 37, in <lambda>
    request.user = SimpleLazyObject(lambda: get_user(request))
                                            ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\contrib\auth\middleware.py", line 17, in get_user
    request._cached_user = auth.get_user(request)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\contrib\auth\__init__.py", line 216, in get_user
    user = backend.get_user(user_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\contrib\auth\backends.py", line 157, in get_user
    user = UserModel._default_manager.get(pk=user_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 645, in get
    num = len(clone)
          ^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 382, in __len__
    self._fetch_all()
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 1928, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
              ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\sql\compiler.py", line 1574, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 354, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: authentication_user.profile_picture
2025-07-19 20:59:34,489 [ERROR] django.server: "POST /api/login/ HTTP/1.1" 500 243612
2025-07-19 20:59:50,414 [ERROR] django.request: Internal Server Error: /api/login/
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 354, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: authentication_user.profile_picture

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 503, in dispatch
    self.initial(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 420, in initial
    self.perform_authentication(request)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 330, in perform_authentication
    request.user
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\request.py", line 232, in user
    self._authenticate()
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\request.py", line 385, in _authenticate
    user_auth_tuple = authenticator.authenticate(self)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\authentication.py", line 127, in authenticate
    if not user or not user.is_active:
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\utils\functional.py", line 251, in inner
    self._setup()
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\utils\functional.py", line 404, in _setup
    self._wrapped = self._setupfunc()
                    ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\contrib\auth\middleware.py", line 37, in <lambda>
    request.user = SimpleLazyObject(lambda: get_user(request))
                                            ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\contrib\auth\middleware.py", line 17, in get_user
    request._cached_user = auth.get_user(request)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\contrib\auth\__init__.py", line 216, in get_user
    user = backend.get_user(user_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\contrib\auth\backends.py", line 157, in get_user
    user = UserModel._default_manager.get(pk=user_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 645, in get
    num = len(clone)
          ^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 382, in __len__
    self._fetch_all()
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 1928, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
              ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\sql\compiler.py", line 1574, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 354, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: authentication_user.profile_picture
2025-07-19 20:59:50,417 [ERROR] django.server: "POST /api/login/ HTTP/1.1" 500 243614
2025-07-19 21:04:00,533 [INFO] django.utils.autoreload: C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\promptRightProd\settings.py changed, reloading.
2025-07-19 21:04:03,956 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-07-19 21:05:09,627 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-07-19 21:05:09,640 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-07-19 21:05:22,808 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-07-19 21:06:08,248 [ERROR] django.request: Internal Server Error: /api/login/
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 354, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: authentication_user.profile_picture

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 503, in dispatch
    self.initial(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 420, in initial
    self.perform_authentication(request)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 330, in perform_authentication
    request.user
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\request.py", line 232, in user
    self._authenticate()
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\request.py", line 385, in _authenticate
    user_auth_tuple = authenticator.authenticate(self)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\authentication.py", line 127, in authenticate
    if not user or not user.is_active:
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\utils\functional.py", line 251, in inner
    self._setup()
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\utils\functional.py", line 404, in _setup
    self._wrapped = self._setupfunc()
                    ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\contrib\auth\middleware.py", line 37, in <lambda>
    request.user = SimpleLazyObject(lambda: get_user(request))
                                            ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\contrib\auth\middleware.py", line 17, in get_user
    request._cached_user = auth.get_user(request)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\contrib\auth\__init__.py", line 216, in get_user
    user = backend.get_user(user_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\contrib\auth\backends.py", line 157, in get_user
    user = UserModel._default_manager.get(pk=user_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 645, in get
    num = len(clone)
          ^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 382, in __len__
    self._fetch_all()
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 1928, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
              ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\sql\compiler.py", line 1574, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 354, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: authentication_user.profile_picture
2025-07-19 21:06:08,288 [ERROR] django.server: "POST /api/login/ HTTP/1.1" 500 243900
2025-07-19 21:07:51,045 [ERROR] django.request: Internal Server Error: /api/login/
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 354, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: authentication_user.profile_picture

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 503, in dispatch
    self.initial(request, *args, **kwargs)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 420, in initial
    self.perform_authentication(request)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\views.py", line 330, in perform_authentication
    request.user
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\request.py", line 232, in user
    self._authenticate()
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\request.py", line 385, in _authenticate
    user_auth_tuple = authenticator.authenticate(self)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\rest_framework\authentication.py", line 127, in authenticate
    if not user or not user.is_active:
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\utils\functional.py", line 251, in inner
    self._setup()
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\utils\functional.py", line 404, in _setup
    self._wrapped = self._setupfunc()
                    ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\contrib\auth\middleware.py", line 37, in <lambda>
    request.user = SimpleLazyObject(lambda: get_user(request))
                                            ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\contrib\auth\middleware.py", line 17, in get_user
    request._cached_user = auth.get_user(request)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\contrib\auth\__init__.py", line 216, in get_user
    user = backend.get_user(user_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\contrib\auth\backends.py", line 157, in get_user
    user = UserModel._default_manager.get(pk=user_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 645, in get
    num = len(clone)
          ^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 382, in __len__
    self._fetch_all()
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 1928, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
              ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\models\sql\compiler.py", line 1574, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\myenv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 354, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: authentication_user.profile_picture
2025-07-19 21:07:51,057 [ERROR] django.server: "POST /api/login/ HTTP/1.1" 500 243900
2025-07-19 21:08:19,929 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-07-19 21:10:08,238 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-07-19 21:10:42,911 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: False
2025-07-19 21:10:43,876 [INFO] authentication.views: Authenticated user: <EMAIL>
2025-07-19 21:10:43,895 [INFO] authentication.views: Roles for user 3: ['organization']
2025-07-19 21:10:43,930 [INFO] django.server: "POST /api/login/ HTTP/1.1" 200 888
2025-07-19 21:10:45,144 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 21:10:45,160 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 21:10:45,437 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 21:10:45,459 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-19 21:10:47,986 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 21:10:48,008 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 21:15:29,767 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-19 21:15:30,762 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-20 18:09:06,323 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-07-20 18:10:28,957 [INFO] django.server: "OPTIONS /api/user/credits/ HTTP/1.1" 200 0
2025-07-20 18:10:28,958 [INFO] django.server: "OPTIONS /api/user/credits/ HTTP/1.1" 200 0
2025-07-20 18:10:29,069 [WARNING] django.server: "GET /api/user/credits/ HTTP/1.1" 401 183
2025-07-20 18:10:29,079 [WARNING] django.server: "GET /api/user/credits/ HTTP/1.1" 401 183
2025-07-20 18:10:29,230 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-20 18:10:29,241 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-20 18:10:37,309 [INFO] django.server: "OPTIONS /api/login/ HTTP/1.1" 200 0
2025-07-20 18:10:37,328 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: False
2025-07-20 18:10:38,156 [INFO] authentication.views: Authenticated user: <EMAIL>
2025-07-20 18:10:38,167 [INFO] authentication.views: Roles for user 3: ['organization']
2025-07-20 18:10:38,171 [INFO] django.server: "POST /api/login/ HTTP/1.1" 200 888
2025-07-20 18:10:38,475 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-20 18:10:38,485 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-20 18:10:38,777 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-20 18:10:38,788 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-20 18:10:48,543 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-20 18:10:48,553 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-20 19:00:09,594 [INFO] django.utils.autoreload: C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\authentication\views.py changed, reloading.
2025-07-20 19:00:16,876 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-07-20 19:00:44,222 [INFO] django.utils.autoreload: C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\authentication\serializers.py changed, reloading.
2025-07-20 19:00:45,782 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-07-20 19:01:44,617 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-07-20 19:13:28,029 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-07-20 19:16:40,952 [INFO] django.utils.autoreload: C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\authentication\views.py changed, reloading.
2025-07-20 19:16:43,048 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-07-20 19:21:42,998 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-07-20 19:22:43,141 [INFO] django.server: "OPTIONS /api/login/ HTTP/1.1" 200 0
2025-07-20 19:22:43,225 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: False
2025-07-20 19:22:43,735 [INFO] authentication.views: Authenticated user: <EMAIL>
2025-07-20 19:22:43,750 [INFO] authentication.views: Roles for user 3: ['organization']
2025-07-20 19:22:43,768 [INFO] django.server: "POST /api/login/ HTTP/1.1" 200 888
2025-07-20 19:22:44,222 [INFO] django.server: "OPTIONS /api/user/credits/ HTTP/1.1" 200 0
2025-07-20 19:22:44,226 [INFO] django.server: "OPTIONS /api/user/credits/ HTTP/1.1" 200 0
2025-07-20 19:22:44,235 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-20 19:22:44,243 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-20 19:22:44,513 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-20 19:22:44,520 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-21 10:06:05,422 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-07-21 10:07:43,137 [INFO] django.server: "OPTIONS /api/user/credits/ HTTP/1.1" 200 0
2025-07-21 10:07:43,137 [INFO] django.server: "OPTIONS /api/user/credits/ HTTP/1.1" 200 0
2025-07-21 10:07:43,270 [WARNING] django.server: "GET /api/user/credits/ HTTP/1.1" 401 183
2025-07-21 10:07:43,282 [WARNING] django.server: "GET /api/user/credits/ HTTP/1.1" 401 183
2025-07-21 10:07:43,389 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-21 10:07:43,418 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-21 10:07:50,119 [INFO] django.server: "OPTIONS /api/login/ HTTP/1.1" 200 0
2025-07-21 10:07:50,139 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: False
2025-07-21 10:07:51,398 [INFO] authentication.views: Authenticated user: <EMAIL>
2025-07-21 10:07:51,419 [INFO] authentication.views: Roles for user 3: ['organization']
2025-07-21 10:07:51,423 [INFO] django.server: "POST /api/login/ HTTP/1.1" 200 888
2025-07-21 10:08:23,186 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-21 10:08:23,214 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-21 10:08:23,227 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-21 10:08:23,256 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-21 10:08:46,194 [INFO] django.server: "OPTIONS /api/organization/students/ HTTP/1.1" 200 0
2025-07-21 10:08:46,203 [INFO] django.server: "OPTIONS /api/organization/students/pending_invitations/ HTTP/1.1" 200 0
2025-07-21 10:08:46,214 [INFO] django.server: "OPTIONS /api/organization/hierarchy-levels/ HTTP/1.1" 200 0
2025-07-21 10:08:46,228 [INFO] django.server: "OPTIONS /api/organization/students/ HTTP/1.1" 200 0
2025-07-21 10:08:46,242 [INFO] django.server: "OPTIONS /api/organization/students/pending_invitations/ HTTP/1.1" 200 0
2025-07-21 10:08:46,254 [INFO] django.server: "OPTIONS /api/organization/hierarchy-levels/ HTTP/1.1" 200 0
2025-07-21 10:08:46,531 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 195
2025-07-21 10:08:46,559 [INFO] django.server: "GET /api/organization/students/pending_invitations/ HTTP/1.1" 200 96
2025-07-21 10:08:46,564 [INFO] django.server: "OPTIONS /api/organization/student-hierarchies/student_hierarchies/?student_id=1 HTTP/1.1" 200 0
2025-07-21 10:08:46,600 [INFO] django.server: "GET /api/organization/hierarchy-levels/ HTTP/1.1" 200 2
2025-07-21 10:08:46,631 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 195
2025-07-21 10:08:46,653 [INFO] django.server: "GET /api/organization/students/pending_invitations/ HTTP/1.1" 200 96
2025-07-21 10:08:46,704 [INFO] django.server: "GET /api/organization/student-hierarchies/student_hierarchies/?student_id=1 HTTP/1.1" 200 96
2025-07-21 10:08:46,708 [INFO] django.server: "GET /api/organization/hierarchy-levels/ HTTP/1.1" 200 2
2025-07-21 10:08:46,787 [INFO] django.server: "GET /api/organization/student-hierarchies/student_hierarchies/?student_id=1 HTTP/1.1" 200 96
2025-07-21 10:08:51,901 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-21 10:08:51,906 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-21 10:08:51,927 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-21 10:08:51,934 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-21 10:08:56,498 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-21 10:08:56,521 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-21 10:21:31,086 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-21 10:21:31,097 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-21 10:21:31,280 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-21 10:21:31,288 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-21 10:21:36,353 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-21 10:21:36,368 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-21 10:26:51,388 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-21 10:27:01,378 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-21 10:27:18,393 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-21 10:27:18,406 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-21 10:27:44,384 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-21 10:27:44,394 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-21 10:27:52,402 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-21 10:27:52,412 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-21 10:28:14,394 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-21 10:28:14,410 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-21 10:28:33,388 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-21 10:28:47,396 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-21 10:28:55,394 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-21 10:29:43,632 [INFO] django.utils.autoreload: C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\authentication\views.py changed, reloading.
2025-07-21 10:29:48,277 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-07-21 10:30:05,106 [INFO] django.utils.autoreload: C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\authentication\views.py changed, reloading.
2025-07-21 10:30:09,215 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-07-21 10:30:28,123 [INFO] django.utils.autoreload: C:\Users\<USER>\Downloads\Projects\grademaster\grade_be\authentication\views.py changed, reloading.
2025-07-21 10:30:32,010 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
