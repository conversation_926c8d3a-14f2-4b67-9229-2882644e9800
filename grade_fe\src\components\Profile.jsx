import React, { useEffect, useState } from "react";
import { useNavigate } from 'react-router-dom';
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { FaCoins, FaSignOutAlt, FaChevronDown, FaChevronUp, FaCamera } from 'react-icons/fa';
import { authService } from './Authentication/authService';
import { PaymentService } from './Upgrade/PaymentService';
import CreditUpgrade from './Upgrade/CreditUpgrade';
import BillingTab from './Billing/BillingTab';
import userProfileImg from '../assets/userProfile.jpeg';

import './Profile.css';

function Profile() {
  const navigate = useNavigate();
  const [userData, setUserData] = useState(null);
  const [credits, setCredits] = useState({
    free_credit: 0,
    paid_credit: 0,
    total_credit: 0,
    loading: true
  });

  const [showUpgrade, setShowUpgrade] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('profile'); // 'profile' or 'billing'
  const activeRole = localStorage.getItem('activeRole');

  useEffect(() => {
    const checkAuthAndLoadData = async () => {
      try {
        // Try to refresh token if needed
        const tokenValid = await authService.refreshTokenIfNeeded();
        if (!tokenValid) {
          throw new Error('Token refresh failed');
        }
        
        // Continue with loading user data
        const storedUser = authService.getCurrentUser();
        if (storedUser) {
          setUserData({
            id: storedUser.id,
            email: storedUser.email,
            username: storedUser.username,
            full_name: storedUser.full_name || storedUser.username,
            role: activeRole,
          });
          
          await loadUserCredits();
        } else {
          throw new Error('User data not found');
        }
      } catch (error) {
        toast.error('Session expired. Please log in again.');
        authService.logout();
        navigate('/authenticate');
      } finally {
        setIsLoading(false);
      }
    };
  
    checkAuthAndLoadData();
  }, [navigate]);

  const loadUserCredits = async () => {
    try {
      setCredits(prev => ({ ...prev, loading: true }));
      const data = await PaymentService.getUserCredits();
      setCredits({
        free_credit: parseFloat(data.free_credit) || 0,
        paid_credit: parseFloat(data.paid_credit) || 0,
        total_credit: parseFloat(data.total_credit) || 0,
        loading: false
      });
    } catch (error) {
      toast.error(error.message || 'Failed to load credit balance');
      setCredits(prev => ({ ...prev, loading: false }));
    }
  };

  const onSelectFile = async (e) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];

      // Create FormData and append the image file
      const formData = new FormData();
      formData.append('profile_picture', file);
      formData.append('user_id', userData.id);

      try {
        const response = await fetch('/api/auth/update_profile', {
          method: 'POST',
          body: formData,
          headers: {
            'Authorization': `Bearer ${authService.getToken()}`
          }
        });

        if (response.ok) {
          const data = await response.json();
          setUserData(prev => ({ ...prev, profile_picture_url: data.profile_picture_url }));

          // Update user data in localStorage
          const updatedUser = { ...userData, profile_picture_url: data.profile_picture_url };
          localStorage.setItem('user', JSON.stringify(updatedUser));
          authService.updateUserCache(updatedUser);

          toast.success('Profile picture updated successfully');
        } else {
          const errorData = await response.json();
          toast.error(errorData.error || 'Failed to update profile picture');
        }
      } catch (error) {
        console.error('Error:', error);
        toast.error('Error uploading profile picture. Please try again.');
      }
    }
  };



  const handleLogout = () => {
    authService.logout();
    toast.success("Logged out successfully");
    navigate('/authenticate');
  };

  if (isLoading) {
    return (
      <div className="profile-loading">
        <div className="bouncing-loader">
          <div></div>
          <div></div>
          <div></div>
        </div>
      </div>
    );
  }

  if (!userData) {
    return null;
  }

  return (
    <div className="profile-page">
      <div className="profile-header">
        <h1>My Profile</h1>
      </div>
      
      <div className="profile-tabs">
        <button 
          className={`profile-tab ${activeTab === 'profile' ? 'active' : ''}`}
          onClick={() => setActiveTab('profile')}
        >
          Profile Information
        </button>
        <button 
          className={`profile-tab ${activeTab === 'billing' ? 'active' : ''}`}
          onClick={() => setActiveTab('billing')}
        >
          Billing & Usage
        </button>
      </div>

      <div className="profile-content">
        {activeTab === 'profile' ? (
          <div className="profile-card">
            <div className="profile-info">
              <div className="profile-photo-container">
                {userData.profile_picture_url ? (
                  <img src={userData.profile_picture_url} alt="Profile" className="profile-photo" />
                ) : (
                  <img src={userProfileImg} alt="Profile" className="profile-photo" />
                )}
                <div className="profile-photo-overlay" onClick={() => document.getElementById('profile-upload').click()}>
                  <FaCamera className="camera-icon" />
                  <input
                    type="file"
                    id="profile-upload"
                    accept="image/*"
                    onChange={onSelectFile}
                    style={{ display: 'none' }}
                  />
                </div>
              </div>


              <div className="profile-text">
                <h2>{userData.full_name}</h2>
                <p className="profile-id">User ID: {userData.id}</p>
                <p className="profile-email">Role: {userData.role}</p>
                <p className="profile-email">Email{userData.email}</p>
              </div>
            </div>

            <div className="credit-card">
              <div className="credit-header">
                <FaCoins className="credit-icon" />
                <h3>Credit Balance</h3>
              </div>
              
              {credits.loading ? (
                <div className="credit-loading">
                  <div className="spinner small"></div>
                </div>
              ) : (
                <div className="credit-details">
                  <div className="credit-row">
                    <span>Free Credits:</span>
                    <span className="credit-amount free">${credits.free_credit.toFixed(7)}</span>
                  </div>
                  <div className="credit-row">
                    <span>Paid Credits:</span>
                    <span className="credit-amount paid">${credits.paid_credit.toFixed(7)}</span>
                  </div>
                  <div className="credit-row total">
                    <span>Total Credits:</span>
                    <span className="credit-amount total">${credits.total_credit.toFixed(7)}</span>
                  </div>
                </div>
              )}
              
              <button 
                className="upgrade-toggle-btn"
                onClick={() => setShowUpgrade(!showUpgrade)}
              >
                {showUpgrade ? (
                  <>
                    <FaChevronUp /> Hide Upgrade Options
                  </>
                ) : (
                  <>
                    <FaChevronDown /> Upgrade Credits
                  </>
                )}
              </button>

              {showUpgrade && (
                <div className="upgrade-section">
                  <CreditUpgrade onUpgradeComplete={loadUserCredits} />
                </div>
              )}
            </div>
          </div>
        ) : (
          <BillingTab />
        )}
        
        <button onClick={handleLogout} className="logout-btn">
          <FaSignOutAlt /> Logout
        </button>
      </div>
    </div>
  );
}

export default Profile;